import { useState } from 'react';
import { Button, Input, Card } from '../components/ui';
import { useLocalStorage } from '../hooks';

const Home: React.FC = () => {
  const [name, setName] = useState('');
  const [savedName, setSavedName] = useLocalStorage('userName', '');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSavedName(name);
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to React + Vite
          </h1>
          <p className="text-gray-600">
            A modern React application with TypeScript, Tailwind CSS, ESLint,
            and Prettier
          </p>
        </div>

        <Card
          title="User Information"
          description="Enter your name to see the components in action"
        >
          <div className="space-y-4">
            <Input
              label="Your Name"
              placeholder="Enter your name"
              value={name}
              onChange={setName}
              required
            />

            <Button
              variant="primary"
              onClick={handleSave}
              loading={loading}
              disabled={!name.trim()}
              className="w-full"
            >
              Save Name
            </Button>

            {savedName && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">
                  Hello, <strong>{savedName}</strong>! Your name has been saved
                  to localStorage.
                </p>
              </div>
            )}
          </div>
        </Card>

        <Card className="mt-6">
          <h3 className="text-lg font-semibold mb-4">Features Included</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              React 19 with TypeScript
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Vite for fast development
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Tailwind CSS for styling
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              ESLint + Prettier for code quality
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Organized folder structure
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Custom hooks and utilities
            </li>
          </ul>
        </Card>

        <div className="mt-6 flex space-x-3">
          <Button variant="outline" className="flex-1">
            Secondary Action
          </Button>
          <Button variant="ghost" className="flex-1">
            Ghost Button
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Home;
