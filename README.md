# React + TypeScript + Vite Project

A modern React application built with best practices and industry-standard tools.

## 🚀 Features

- **React 19** - Latest React with concurrent features
- **TypeScript** - Type safety and better developer experience
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework with custom design system
- **ESLint** - Code linting with React and TypeScript rules
- **Prettier** - Code formatting
- **Custom Hooks** - Reusable logic with useLocalStorage example
- **Component Library** - Reusable UI components (Button, Input, Card)
- **Organized Structure** - Clean folder organization following best practices

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   └── ui/             # Basic UI components (Button, Input, Card)
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── assets/             # Static assets (images, icons)
├── styles/             # Additional CSS files
├── services/           # API services and external integrations
└── context/            # React context providers
```

## 🛠️ Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

## 🎨 Styling

This project uses Tailwind CSS with a custom design system:

- **Custom Colors**: Primary color palette defined in `tailwind.config.js`
- **Component Classes**: Reusable component styles in `src/index.css`
- **Utility Functions**: `cn()` function for conditional class merging

## 🧩 Components

### UI Components

- **Button**: Configurable button with variants (primary, secondary, outline, ghost)
- **Input**: Form input with label, validation, and error states
- **Card**: Container component with optional title and description

### Usage Example

```tsx
import { Button, Input, Card } from './components/ui';

function MyComponent() {
  return (
    <Card title="Example" description="This is a card">
      <Input label="Name" value={name} onChange={setName} required />
      <Button variant="primary" onClick={handleSubmit}>
        Submit
      </Button>
    </Card>
  );
}
```

## 🪝 Custom Hooks

### useLocalStorage

A hook for managing localStorage with React state synchronization:

```tsx
import { useLocalStorage } from './hooks';

function MyComponent() {
  const [value, setValue] = useLocalStorage('key', 'defaultValue');

  return <input value={value} onChange={e => setValue(e.target.value)} />;
}
```

## 🔧 Configuration

### ESLint

Configured with:

- React and React Hooks rules
- TypeScript support
- Prettier integration
- Custom rules for code quality

### Prettier

Configured for consistent code formatting with:

- Single quotes
- Semicolons
- 2-space indentation
- 80 character line width

### Tailwind CSS

Custom configuration includes:

- Extended color palette
- Custom font family (Inter)
- Typography plugin
- Component and utility layers

## 🚀 Deployment

1. Build the project:

   ```bash
   npm run build
   ```

2. The `dist` folder contains the production-ready files

3. Deploy to your preferred hosting service (Vercel, Netlify, etc.)

## 🤝 Contributing

1. Follow the existing code style
2. Run `npm run lint` and `npm run format` before committing
3. Ensure TypeScript types are properly defined
4. Add tests for new features

## 📝 License

This project is licensed under the MIT License.
